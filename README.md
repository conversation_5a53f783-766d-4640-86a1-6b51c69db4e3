# DeepSeek-Coder Ollama 客户端

这是一个Python项目，使用OpenAI库访问通过Ollama部署的DeepSeek-Coder模型，提供交互式代码生成功能。
pip3 install openai --disable-pip-version-check --no-cache-dir --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org
## 项目结构

```
demo_1/
├── main.py                # 主程序文件 - 使用OpenAI库的Ollama客户端
├── requirements.txt       # 项目依赖 (openai库)
├── mcp_server.py         # MCP服务器 - 提供a+b计算功能
├── mcp_client.py         # MCP客户端测试工具
├── mcp_config.json       # MCP配置文件
├── demo_mcp.py           # MCP服务器演示脚本
├── openai_mcp_client.py  # OpenAI + MCP 完整集成客户端
├── demo_openai_mcp.py    # OpenAI + MCP 标准工具调用演示
├── manual_openai_mcp.py  # OpenAI + MCP 手动集成版本
├── simple_openai_mcp.py  # OpenAI + MCP 简单集成版本 (推荐)
├── MCP_README.md         # MCP服务器详细说明
└── README.md            # 项目说明
```

## 功能

### DeepSeek-Coder Ollama 客户端
- **Ollama连接测试**：检查Ollama服务是否正常运行
- **模型列表查看**：显示所有可用的模型
- **交互式代码生成**：使用DeepSeek-Coder生成代码
- **流式输出**：实时显示模型生成的内容
- **OpenAI兼容API**：使用标准OpenAI库接口

### MCP Server (Model Context Protocol)
- **加法计算**：提供 a + b 的计算功能
- **标准MCP协议**：支持工具列表、工具调用等
- **错误处理**：完善的参数验证和错误响应
- **Claude Desktop集成**：可与Claude Desktop等MCP客户端集成

### OpenAI + MCP 集成
- **工具调用集成**：在OpenAI对话中自动调用MCP工具
- **多种集成方式**：标准工具调用、手动集成、简单集成
- **智能识别**：自动识别计算需求并调用相应工具
- **交互式聊天**：支持实时对话和工具调用

## 前置要求

1. **安装Ollama**：
   ```bash
   # macOS
   brew install ollama

   # 或从官网下载: https://ollama.ai
   ```

2. **启动Ollama服务**：
   ```bash
   ollama serve
   ```

3. **下载DeepSeek-Coder模型**：
   ```bash
   ollama pull deepseek-coder
   ```

## 安装和运行

### 安装依赖
```bash
# 安装OpenAI库
pip3 install openai

# 或者使用requirements.txt
pip3 install -r requirements.txt
```

### 运行程序

#### DeepSeek-Coder 客户端
```bash
python3 main.py
```

#### MCP Server 测试
```bash
# 测试MCP服务器
python3 mcp_client.py

# MCP服务器演示
python3 demo_mcp.py

# 或直接启动MCP服务器
python3 mcp_server.py
```

#### OpenAI + MCP 集成
```bash
# 简单集成演示 (推荐)
python3 simple_openai_mcp.py

# 交互式聊天
python3 simple_openai_mcp.py chat

# 手动集成演示
python3 manual_openai_mcp.py

# 完整集成客户端
python3 openai_mcp_client.py
```

## 使用说明

运行程序后，您可以：

1. **测试连接**：程序会自动检查Ollama连接状态
2. **查看模型**：选择选项2查看所有可用模型
3. **代码生成**：选择选项1进入交互式代码生成模式
   - 输入您的代码需求
   - 模型会实时生成代码
   - 输入'quit'退出

## 示例

```
🚀 DeepSeek-Coder Ollama 客户端
========================================
测试Ollama连接...
✅ Ollama连接成功!
可用模型:
  - deepseek-coder

请选择功能:
1. 交互式代码生成
2. 查看可用模型
3. 测试简单功能
4. 退出

请输入选项 (1-4): 1

🤖 使用模型: deepseek-coder
输入您的代码需求，输入'quit'退出
--------------------------------------------------

请描述您需要的代码: 写一个Python函数计算斐波那契数列

🔄 正在生成代码...
------------------------------
🤖 AI回复:
def fibonacci(n):
    """
    计算斐波那契数列的第n项
    """
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        return fibonacci(n-1) + fibonacci(n-2)
```

## 故障排除

1. **连接失败**：
   - 确保Ollama服务正在运行 (`ollama serve`)
   - 检查端口11434是否被占用

2. **模型未找到**：
   - 运行 `ollama pull deepseek-coder` 下载模型
   - 使用 `ollama list` 查看已安装模型

3. **依赖问题**：
   - 确保Python版本 >= 3.7
   - 运行 `pip install --upgrade openai`

## MCP Server 使用

### 快速测试
```bash
# 运行自动化测试
python3 mcp_client.py
```

### 与Claude Desktop集成
1. 找到Claude Desktop配置文件：
   - **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

2. 添加MCP服务器配置：
```json
{
  "mcpServers": {
    "addition-server": {
      "command": "python3",
      "args": ["/path/to/your/mcp_server.py"],
      "cwd": "/path/to/your/directory"
    }
  }
}
```

3. 重启Claude Desktop，即可在对话中使用加法工具

### MCP工具使用示例
在Claude Desktop中，您可以这样使用：
- "请帮我计算 15 + 27"
- "用加法工具算一下 3.14 + 2.86"

详细说明请参考 [MCP_README.md](MCP_README.md)

## OpenAI + MCP 集成使用

### 简单集成 (推荐)
最简单易用的集成方式，直接返回MCP工具计算结果：

```bash
# 演示模式
python3 simple_openai_mcp.py

# 交互式聊天
python3 simple_openai_mcp.py chat
```

**特点**：
- ✅ 自动识别加法计算请求
- ✅ 直接返回准确的计算结果
- ✅ 支持负数和小数
- ✅ 快速响应，无需AI重新处理

**使用示例**：
```
👤 您: 计算 15 + 27
🤖 AI: ✅ 计算结果: 15.0 + 27.0 = 42.0

👤 您: 5.5加上4.5
🤖 AI: ✅ 计算结果: 5.5 + 4.5 = 10.0
```

### 手动集成
当模型不支持标准工具调用时的备选方案：

```bash
python3 manual_openai_mcp.py
```

### 完整集成
支持标准OpenAI工具调用协议的完整实现：

```bash
python3 openai_mcp_client.py
```

### 集成原理

1. **MCP服务器启动**：后台启动MCP服务器提供计算服务
2. **文本解析**：使用正则表达式识别用户的计算请求
3. **工具调用**：自动调用MCP工具执行计算
4. **结果返回**：将计算结果整合到AI回复中

### 支持的计算格式

- `计算 15 + 27`
- `15 + 27`
- `15加上27`
- `15.5 + 2.5`
- `-10 + 25`
- `100 + (-50)`

### 扩展建议

您可以轻松扩展这个集成来支持更多MCP工具：
1. 在MCP服务器中添加新工具（减法、乘法、除法等）
2. 在集成客户端中添加相应的文本解析逻辑
3. 更新工具调用处理函数

## 开发

要扩展此项目，您可以：
- 在main.py中添加更多函数
- 在requirements.txt中添加需要的依赖包
- 创建更多模块文件
