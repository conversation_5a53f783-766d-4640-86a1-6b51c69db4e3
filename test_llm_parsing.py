#!/usr/bin/env python3
"""
测试LLM解析功能
"""

from openai import OpenAI

def test_llm_parsing():
    """测试LLM解析数学表达式"""
    client = OpenAI(
        base_url="http://localhost:11434/v1",
        api_key="ollama"
    )
    
    print("🧪 测试LLM数学表达式解析")
    print("=" * 40)
    
    test_cases = [
        "计算 15 + 27",
        "请算一下 3.14 + 2.86",
        "5.5加上4.5",
        "-10 + 25",
        "今天天气怎么样？",
        "你好，你能做什么？"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ 测试: {test_input}")
        print("-" * 30)
        
        try:
            prompt = f"""分析用户输入是否包含加法计算。如果是，提取两个数字。

用户输入: {test_input}

如果是加法计算，回复格式: ADD:数字1,数字2
如果不是加法计算，回复: NOT_ADD

示例:
- "计算 15 + 27" → ADD:15,27
- "15加上27" → ADD:15,27  
- "3.14 + 2.86" → ADD:3.14,2.86
- "-10 + 25" → ADD:-10,25
- "今天天气怎么样" → NOT_ADD"""

            print("🔄 正在调用LLM...")
            response = client.chat.completions.create(
                model="deepseek-r1:1.5b",
                messages=[
                    {"role": "system", "content": "你是数学表达式解析器。严格按照要求的格式回复，不要添加解释。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            result = response.choices[0].message.content.strip()
            print(f"🔍 LLM回复: {result}")
            
            # 解析结果
            if result.startswith("ADD:"):
                numbers_str = result[4:]  # 去掉 "ADD:" 前缀
                try:
                    parts = numbers_str.split(",")
                    if len(parts) == 2:
                        a = float(parts[0].strip())
                        b = float(parts[1].strip())
                        print(f"✅ 解析成功: {a} + {b}")
                    else:
                        print(f"❌ 格式错误: {parts}")
                except ValueError as e:
                    print(f"❌ 数字解析失败: {e}")
            elif result == "NOT_ADD":
                print("✅ 正确识别为非计算请求")
            else:
                print(f"❌ 未知格式: {result}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_llm_parsing()
