#!/usr/bin/env python3
"""
LangChain SQL Agent
使用LangChain的SQL Agent实现数据库查询功能
"""

import sqlite3
import os
from langchain_community.llms import Ollama
from langchain_community.utilities import SQLDatabase
from langchain_community.agent_toolkits import create_sql_agent
from langchain.agents.agent_types import AgentType

class LangChainSQLAgent:
    """LangChain SQL Agent"""
    
    def __init__(self, model_name: str = "deepseek-coder", db_path: str = "demo.db"):
        """
        初始化LangChain SQL Agent
        
        Args:
            model_name (str): Ollama模型名称
            db_path (str): SQLite数据库路径
        """
        self.model_name = model_name
        self.db_path = db_path
        
        # 创建示例数据库
        self.create_demo_database()
        
        # 初始化Ollama LLM
        self.llm = Ollama(
            model=model_name,
            base_url="http://localhost:11434",
            temperature=0.1
        )
        
        # 初始化数据库连接
        self.db = SQLDatabase.from_uri(f"sqlite:///{db_path}")
        
        # 创建SQL Agent
        self.agent_executor = create_sql_agent(
            llm=self.llm,
            db=self.db,
            agent_type=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True,
            handle_parsing_errors=True
        )
    
    def create_demo_database(self):
        """创建示例数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建用户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                age INTEGER,
                city TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建订单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                product_name TEXT NOT NULL,
                quantity INTEGER DEFAULT 1,
                price DECIMAL(10,2),
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # 清空现有数据
        cursor.execute('DELETE FROM orders')
        cursor.execute('DELETE FROM users')
        
        # 插入示例数据
        users_data = [
            ('张三', '<EMAIL>', 25, '北京'),
            ('李四', '<EMAIL>', 30, '上海'),
            ('王五', '<EMAIL>', 28, '广州'),
            ('赵六', '<EMAIL>', 35, '深圳'),
            ('钱七', '<EMAIL>', 22, '杭州')
        ]
        
        cursor.executemany(
            'INSERT INTO users (name, email, age, city) VALUES (?, ?, ?, ?)',
            users_data
        )
        
        orders_data = [
            (1, '笔记本电脑', 1, 5999.00),
            (1, '鼠标', 2, 99.00),
            (2, '手机', 1, 3999.00),
            (2, '耳机', 1, 299.00),
            (3, '键盘', 1, 399.00),
            (4, '显示器', 2, 1299.00),
            (5, '平板电脑', 1, 2999.00)
        ]
        
        cursor.executemany(
            'INSERT INTO orders (user_id, product_name, quantity, price) VALUES (?, ?, ?, ?)',
            orders_data
        )
        
        conn.commit()
        conn.close()
        print("✅ 示例数据库创建完成")
    
    def query(self, question: str) -> str:
        """
        使用LangChain SQL Agent查询数据库
        
        Args:
            question (str): 自然语言问题
            
        Returns:
            str: 查询结果
        """
        try:
            print(f"🔍 问题: {question}")
            result = self.agent_executor.run(question)
            return result
        except Exception as e:
            return f"查询失败: {str(e)}"
    
    def get_database_info(self) -> str:
        """获取数据库信息"""
        try:
            return self.db.get_table_info()
        except Exception as e:
            return f"获取数据库信息失败: {str(e)}"

def main():
    """主函数"""
    print("🚀 LangChain SQL Agent")
    print("=" * 40)
    
    try:
        # 初始化Agent
        print("🔧 初始化LangChain SQL Agent...")
        agent = LangChainSQLAgent()
        
        # 显示数据库信息
        print("\n📊 数据库信息:")
        print("-" * 30)
        db_info = agent.get_database_info()
        print(db_info)
        
        # 示例查询
        print("\n🧪 示例查询:")
        print("-" * 30)
        
        test_questions = [
            "有多少个用户？",
            "显示所有用户的姓名和城市",
            "总订单金额是多少？",
            "张三买了什么产品？",
            "平均订单金额是多少？"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{i}️⃣ 测试查询: {question}")
            print("-" * 20)
            try:
                answer = agent.query(question)
                print(f"📋 回答: {answer}")
            except Exception as e:
                print(f"❌ 查询失败: {e}")
        
        # 交互式查询
        print("\n💬 交互式查询模式")
        print("输入自然语言问题，输入'quit'退出")
        print("-" * 40)
        
        while True:
            try:
                user_question = input("\n🤔 您的问题: ").strip()
                
                if user_question.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见!")
                    break
                
                if not user_question:
                    print("请输入有效的问题!")
                    continue
                
                print()
                answer = agent.query(user_question)
                print(f"\n📋 回答: {answer}")
                
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"\n❌ 错误: {e}")
    
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        print("请确保:")
        print("1. Ollama服务正在运行")
        print("2. deepseek-coder模型已下载")
        print("3. 已安装langchain相关依赖")

if __name__ == "__main__":
    main()
