#!/usr/bin/env python3
"""
OpenAI + MCP 集成客户端
在OpenAI对话中使用MCP服务器的工具
"""

import asyncio
import json
import subprocess
import sys
from typing import Any, Dict, List, Optional
from openai import OpenAI

class MCPToolManager:
    """MCP工具管理器"""
    
    def __init__(self, server_command: List[str]):
        self.server_command = server_command
        self.process = None
        self.request_id = 0
        self.tools = {}
    
    async def start_server(self):
        """启动MCP服务器"""
        self.process = await asyncio.create_subprocess_exec(
            *self.server_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        print("🔧 MCP服务器已启动")
    
    async def initialize(self):
        """初始化MCP连接"""
        response = await self.send_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "openai-mcp-client",
                "version": "1.0.0"
            }
        })
        
        if "error" in response:
            raise RuntimeError(f"MCP初始化失败: {response['error']}")
        
        print("✅ MCP连接初始化成功")
        return response["result"]
    
    async def load_tools(self):
        """加载MCP工具"""
        response = await self.send_request("tools/list")
        
        if "error" in response:
            raise RuntimeError(f"获取MCP工具失败: {response['error']}")
        
        mcp_tools = response["result"]["tools"]
        
        # 转换为OpenAI函数格式
        for tool in mcp_tools:
            self.tools[tool["name"]] = {
                "mcp_tool": tool,
                "openai_function": {
                    "type": "function",
                    "function": {
                        "name": tool["name"],
                        "description": tool["description"],
                        "parameters": tool["inputSchema"]
                    }
                }
            }
        
        print(f"📦 已加载 {len(self.tools)} 个MCP工具")
        return list(self.tools.values())
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """调用MCP工具"""
        response = await self.send_request("tools/call", {
            "name": tool_name,
            "arguments": arguments
        })
        
        if "error" in response:
            return f"工具调用失败: {response['error']['message']}"
        
        # 提取文本内容
        content = response["result"].get("content", [])
        if content and content[0].get("type") == "text":
            return content[0]["text"]
        else:
            return str(response["result"])
    
    async def send_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送MCP请求"""
        if not self.process:
            raise RuntimeError("MCP服务器未启动")
        
        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        
        if params:
            request["params"] = params
        
        # 发送请求
        request_json = json.dumps(request) + "\n"
        self.process.stdin.write(request_json.encode())
        await self.process.stdin.drain()
        
        # 读取响应
        response_line = await self.process.stdout.readline()
        response_text = response_line.decode().strip()
        
        if not response_text:
            raise RuntimeError("MCP服务器无响应")
        
        return json.loads(response_text)
    
    async def close(self):
        """关闭MCP连接"""
        if self.process:
            self.process.terminate()
            await self.process.wait()
            print("🔧 MCP服务器已关闭")
    
    def get_openai_tools(self) -> List[Dict[str, Any]]:
        """获取OpenAI格式的工具定义"""
        return [tool["openai_function"] for tool in self.tools.values()]

class OpenAIMCPClient:
    """OpenAI + MCP 集成客户端"""
    
    def __init__(self, openai_base_url="http://localhost:11434/v1", openai_api_key="ollama"):
        # 初始化OpenAI客户端
        self.openai_client = OpenAI(
            base_url=openai_base_url,
            api_key=openai_api_key
        )
        
        # 初始化MCP工具管理器
        self.mcp_manager = MCPToolManager([sys.executable, "mcp_server.py"])
        
        self.conversation_history = []
    
    async def start(self):
        """启动客户端"""
        print("🚀 启动 OpenAI + MCP 集成客户端")
        print("=" * 50)
        
        # 启动MCP服务器
        await self.mcp_manager.start_server()
        await self.mcp_manager.initialize()
        await self.mcp_manager.load_tools()
        
        print("✅ 所有服务已就绪!")
    
    async def chat_with_tools(self, user_message: str, model: str = "deepseek-coder") -> str:
        """与AI对话，支持工具调用"""
        # 添加用户消息到历史
        self.conversation_history.append({
            "role": "user",
            "content": user_message
        })
        
        # 获取可用工具
        tools = self.mcp_manager.get_openai_tools()
        
        try:
            # 调用OpenAI API
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=self.conversation_history,
                tools=tools if tools else None,
                tool_choice="auto" if tools else None,
                temperature=0.1
            )
            
            message = response.choices[0].message
            
            # 处理工具调用
            if message.tool_calls:
                # 添加助手消息到历史
                self.conversation_history.append({
                    "role": "assistant",
                    "content": message.content,
                    "tool_calls": [
                        {
                            "id": tc.id,
                            "type": tc.type,
                            "function": {
                                "name": tc.function.name,
                                "arguments": tc.function.arguments
                            }
                        } for tc in message.tool_calls
                    ]
                })
                
                # 执行工具调用
                for tool_call in message.tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    print(f"🔧 调用工具: {function_name}({function_args})")
                    
                    # 调用MCP工具
                    tool_result = await self.mcp_manager.call_tool(function_name, function_args)
                    
                    # 添加工具结果到历史
                    self.conversation_history.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": tool_result
                    })
                
                # 获取最终响应
                final_response = self.openai_client.chat.completions.create(
                    model=model,
                    messages=self.conversation_history,
                    temperature=0.1
                )
                
                final_message = final_response.choices[0].message.content
                
                # 添加最终响应到历史
                self.conversation_history.append({
                    "role": "assistant",
                    "content": final_message
                })
                
                return final_message
            else:
                # 没有工具调用，直接返回响应
                self.conversation_history.append({
                    "role": "assistant",
                    "content": message.content
                })
                
                return message.content
                
        except Exception as e:
            return f"对话失败: {str(e)}"
    
    async def interactive_chat(self):
        """交互式对话"""
        print("\n💬 开始交互式对话")
        print("输入 'quit' 退出，输入 'clear' 清空对话历史")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n👤 您: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("再见!")
                    break
                
                if user_input.lower() in ['clear', '清空']:
                    self.conversation_history = []
                    print("✅ 对话历史已清空")
                    continue
                
                if not user_input:
                    print("请输入有效的消息!")
                    continue
                
                print("\n🤖 AI: ", end="", flush=True)
                response = await self.chat_with_tools(user_input)
                print(response)
                
            except KeyboardInterrupt:
                print("\n\n对话被用户中断")
                break
            except Exception as e:
                print(f"\n❌ 错误: {e}")
    
    async def close(self):
        """关闭客户端"""
        await self.mcp_manager.close()

async def main():
    """主函数"""
    client = OpenAIMCPClient()
    
    try:
        await client.start()
        await client.interactive_chat()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
    finally:
        await client.close()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序失败: {e}")
        sys.exit(1)
