#!/usr/bin/env python3
"""
使用OpenAI库访问Ollama部署的DeepSeek-Coder模型
"""

from openai import OpenAI

class OllamaOpenAIClient:
    """使用OpenAI库连接Ollama的客户端类"""

    def __init__(self, base_url="http://localhost:11434/v1", api_key="ollama"):
        """
        初始化Ollama客户端

        Args:
            base_url (str): Ollama服务的OpenAI兼容API URL
            api_key (str): API密钥（Ollama不需要真实密钥，但OpenAI库需要）
        """
        self.client = OpenAI(
            base_url=base_url,
            api_key=api_key
        )
        self.base_url = base_url

    def list_models(self):
        """获取可用模型列表"""
        try:
            models = self.client.models.list()
            return [model.id for model in models.data]
        except Exception as e:
            print(f"获取模型列表失败: {e}")
            return None
    
    def generate_code(self, model_name, prompt, stream=False):
        """
        使用指定模型生成代码

        Args:
            model_name (str): 模型名称，如 'deepseek-coder'
            prompt (str): 输入提示
            stream (bool): 是否使用流式输出
        """
        try:
            messages = [
                {"role": "system", "content": "你是一个专业的代码助手，请根据用户需求生成高质量的代码。"},
                {"role": "user", "content": prompt}
            ]

            if stream:
                # 流式输出
                stream_response = self.client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    stream=True,
                    temperature=0.1
                )

                print("🤖 AI回复:")
                for chunk in stream_response:
                    if chunk.choices[0].delta.content is not None:
                        print(chunk.choices[0].delta.content, end='', flush=True)
                print()  # 换行
            else:
                # 非流式输出
                response = self.client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    temperature=0.1
                )
                return response.choices[0].message.content

        except Exception as e:
            print(f"代码生成失败: {e}")
            return None
    
    def chat_with_model(self, model_name, messages):
        """
        与模型进行对话

        Args:
            model_name (str): 模型名称
            messages (list): 对话消息列表，格式为OpenAI消息格式
        """
        try:
            response = self.client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=0.1
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"对话请求失败: {e}")
            return None

def test_ollama_connection():
    """测试Ollama连接"""
    print("测试Ollama连接...")
    client = OllamaOpenAIClient()

    # 获取模型列表
    models = client.list_models()
    if models:
        print("✅ Ollama连接成功!")
        print("可用模型:")
        for model in models:
            print(f"  - {model}")
        return True
    else:
        print("❌ Ollama连接失败!")
        return False

def interactive_code_generation():
    """交互式代码生成"""
    client = OllamaOpenAIClient()
    model_name = "deepseek-coder"  # 默认使用deepseek-coder
    
    print(f"\n🤖 使用模型: {model_name}")
    print("输入您的代码需求，输入'quit'退出")
    print("-" * 50)
    
    while True:
        try:
            prompt = input("\n请描述您需要的代码: ").strip()
            
            if prompt.lower() in ['quit', 'exit', '退出']:
                print("再见!")
                break
            
            if not prompt:
                print("请输入有效的提示!")
                continue
            
            print(f"\n🔄 正在生成代码...")
            print("-" * 30)
            
            # 构造更好的提示
            full_prompt = f"请帮我写一段代码: {prompt}\n\n请提供完整的、可运行的代码，并包含必要的注释。"
            
            # 使用流式输出生成代码
            client.generate_code(model_name, full_prompt, stream=True)
            
            print("\n" + "-" * 30)
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

def greet(name):
    """问候函数"""
    return f"你好, {name}!"

def calculate_sum(a, b):
    """计算两个数的和"""
    return a + b

def main():
    """主函数"""
    print("🚀 DeepSeek-Coder Ollama 客户端 (OpenAI库版本)")
    print("=" * 50)
    
    # 测试连接
    if not test_ollama_connection():
        print("\n请确保:")
        print("1. Ollama已经安装并运行")
        print("2. DeepSeek-Coder模型已下载 (ollama pull deepseek-coder)")
        print("3. Ollama服务在localhost:11434运行")
        return
    
    # 显示菜单
    while True:
        print("\n" + "=" * 50)
        print("请选择功能:")
        print("1. 交互式代码生成")
        print("2. 查看可用模型")
        print("3. 测试简单功能")
        print("4. 退出")
        
        try:
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == "1":
                interactive_code_generation()
            elif choice == "2":
                client = OllamaOpenAIClient()
                models = client.list_models()
                if models:
                    print("\n可用模型:")
                    for model in models:
                        print(f"  📦 {model}")
                else:
                    print("无法获取模型列表")
            elif choice == "3":
                # 测试简单功能
                name = "世界"
                greeting = greet(name)
                print(f"\n{greeting}")
                
                num1, num2 = 10, 20
                result = calculate_sum(num1, num2)
                print(f"{num1} + {num2} = {result}")
            elif choice == "4":
                print("再见!")
                break
            else:
                print("无效选项，请重新选择!")
                
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")
    
    print("\n程序运行完成!")

if __name__ == "__main__":
    main()
