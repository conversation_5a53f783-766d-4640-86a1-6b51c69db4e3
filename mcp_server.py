#!/usr/bin/env python3
"""
MCP Server 实现 - 提供 a + b 计算功能
Model Context Protocol Server for addition operations
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional

class MCPServer:
    """MCP Server 实现"""
    
    def __init__(self):
        self.tools = {
            "add": {
                "name": "add",
                "description": "计算两个数字的和 (a + b)",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "a": {
                            "type": "number",
                            "description": "第一个数字"
                        },
                        "b": {
                            "type": "number", 
                            "description": "第二个数字"
                        }
                    },
                    "required": ["a", "b"]
                }
            }
        }
    
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理MCP请求"""
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")
        
        try:
            if method == "initialize":
                return await self.handle_initialize(request_id, params)
            elif method == "tools/list":
                return await self.handle_tools_list(request_id)
            elif method == "tools/call":
                return await self.handle_tools_call(request_id, params)
            else:
                return self.create_error_response(request_id, -32601, f"Method not found: {method}")
        except Exception as e:
            return self.create_error_response(request_id, -32603, f"Internal error: {str(e)}")
    
    async def handle_initialize(self, request_id: Optional[str], params: Dict[str, Any]) -> Dict[str, Any]:
        """处理初始化请求"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "serverInfo": {
                    "name": "addition-server",
                    "version": "1.0.0"
                }
            }
        }
    
    async def handle_tools_list(self, request_id: Optional[str]) -> Dict[str, Any]:
        """处理工具列表请求"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "tools": list(self.tools.values())
            }
        }
    
    async def handle_tools_call(self, request_id: Optional[str], params: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具调用请求"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if tool_name == "add":
            return await self.handle_add(request_id, arguments)
        else:
            return self.create_error_response(request_id, -32602, f"Unknown tool: {tool_name}")
    
    async def handle_add(self, request_id: Optional[str], arguments: Dict[str, Any]) -> Dict[str, Any]:
        """处理加法计算"""
        try:
            a = arguments.get("a")
            b = arguments.get("b")
            
            if a is None or b is None:
                return self.create_error_response(request_id, -32602, "Missing required parameters 'a' or 'b'")
            
            # 确保参数是数字
            if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):
                return self.create_error_response(request_id, -32602, "Parameters 'a' and 'b' must be numbers")
            
            result = a + b
            
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": f"计算结果: {a} + {b} = {result}"
                        }
                    ]
                }
            }
        except Exception as e:
            return self.create_error_response(request_id, -32603, f"Calculation error: {str(e)}")
    
    def create_error_response(self, request_id: Optional[str], code: int, message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": code,
                "message": message
            }
        }

class StdioTransport:
    """标准输入输出传输层"""
    
    def __init__(self):
        self.server = MCPServer()
    
    async def run(self):
        """运行服务器"""
        print("🚀 MCP Addition Server 启动", file=sys.stderr)
        print("等待客户端连接...", file=sys.stderr)
        
        while True:
            try:
                # 从标准输入读取请求
                line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
                if not line:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                # 解析JSON请求
                try:
                    request = json.loads(line)
                except json.JSONDecodeError as e:
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": f"Parse error: {str(e)}"
                        }
                    }
                    print(json.dumps(error_response), flush=True)
                    continue
                
                # 处理请求
                response = await self.server.handle_request(request)
                
                # 发送响应到标准输出
                print(json.dumps(response), flush=True)
                
            except KeyboardInterrupt:
                print("\n服务器被用户中断", file=sys.stderr)
                break
            except Exception as e:
                print(f"服务器错误: {e}", file=sys.stderr)
                break

def main():
    """主函数"""
    transport = StdioTransport()
    try:
        asyncio.run(transport.run())
    except KeyboardInterrupt:
        print("\n服务器关闭", file=sys.stderr)
    except Exception as e:
        print(f"启动失败: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
