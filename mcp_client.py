#!/usr/bin/env python3
"""
MCP Client 测试工具
用于测试 MCP Addition Server
"""

import asyncio
import json
import subprocess
import sys
from typing import Any, Dict, Optional

class MCPClient:
    """MCP 客户端"""
    
    def __init__(self, server_command: list):
        self.server_command = server_command
        self.process = None
        self.request_id = 0
    
    async def start_server(self):
        """启动MCP服务器"""
        self.process = await asyncio.create_subprocess_exec(
            *self.server_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        print("✅ MCP服务器已启动")
    
    async def send_request(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送请求到MCP服务器"""
        if not self.process:
            raise RuntimeError("服务器未启动")
        
        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        
        if params:
            request["params"] = params
        
        # 发送请求
        request_json = json.dumps(request) + "\n"
        self.process.stdin.write(request_json.encode())
        await self.process.stdin.drain()
        
        # 读取响应
        response_line = await self.process.stdout.readline()
        response_text = response_line.decode().strip()
        
        if not response_text:
            raise RuntimeError("服务器无响应")
        
        return json.loads(response_text)
    
    async def initialize(self):
        """初始化连接"""
        response = await self.send_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        })
        
        if "error" in response:
            raise RuntimeError(f"初始化失败: {response['error']}")
        
        print("✅ 初始化成功")
        return response["result"]
    
    async def list_tools(self):
        """获取工具列表"""
        response = await self.send_request("tools/list")
        
        if "error" in response:
            raise RuntimeError(f"获取工具列表失败: {response['error']}")
        
        return response["result"]["tools"]
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """调用工具"""
        response = await self.send_request("tools/call", {
            "name": tool_name,
            "arguments": arguments
        })
        
        if "error" in response:
            raise RuntimeError(f"工具调用失败: {response['error']}")
        
        return response["result"]
    
    async def close(self):
        """关闭连接"""
        if self.process:
            self.process.terminate()
            await self.process.wait()
            print("✅ 服务器已关闭")

async def test_addition_server():
    """测试服务器"""
    print("🧪 开始测试 MCP Addition Server")
    print("=" * 50)
    
    # 创建客户端
    client = MCPClient([sys.executable, "mcp_server.py"])
    
    try:
        # 启动服务器
        await client.start_server()
        
        # 初始化
        init_result = await client.initialize()
        print(f"服务器信息: {init_result.get('serverInfo', {})}")
        
        # 获取工具列表
        tools = await client.list_tools()
        print(f"\n可用工具: {len(tools)} 个")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description']}")
        
        # 测试加法功能
        print("\n🔢 测试加法功能:")
        test_cases = [
            {"a": 5, "b": 3},
            {"a": 10.5, "b": 2.5},
            {"a": -5, "b": 8},
            {"a": 0, "b": 0},
            {"a": 100, "b": -50}
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {test_case['a']} + {test_case['b']}")
            try:
                result = await client.call_tool("add", test_case)
                content = result.get("content", [])
                if content:
                    print(f"  结果: {content[0].get('text', 'No text')}")
                else:
                    print(f"  结果: {result}")
            except Exception as e:
                print(f"  错误: {e}")
        
        # 测试错误情况
        print("\n❌ 测试错误情况:")
        error_cases = [
            {"a": "not_a_number", "b": 5},
            {"a": 5},  # 缺少参数b
            {}  # 缺少所有参数
        ]
        
        for i, error_case in enumerate(error_cases, 1):
            print(f"\n错误测试 {i}: {error_case}")
            try:
                result = await client.call_tool("add", error_case)
                print(f"  意外成功: {result}")
            except Exception as e:
                print(f"  预期错误: {e}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        await client.close()
    
    print("\n🎉 测试完成!")

def main():
    """主函数"""
    try:
        asyncio.run(test_addition_server())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
