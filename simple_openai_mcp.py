#!/usr/bin/env python3
"""
简单的OpenAI + MCP集成
直接使用MCP工具结果，避免AI模型的复杂回复
"""

import asyncio
import json
import subprocess
import sys
from openai import OpenAI

class SimpleOpenAIMCP:
    """简单的OpenAI + MCP集成"""
    
    def __init__(self):
        self.openai_client = OpenAI(
            base_url="http://localhost:11434/v1",
            api_key="ollama"
        )
        self.mcp_process = None
        self.request_id = 0
    
    async def start_mcp_server(self):
        """启动MCP服务器"""
        self.mcp_process = await asyncio.create_subprocess_exec(
            sys.executable, "mcp_server.py",
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # 初始化MCP
        await self.send_mcp_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {"name": "simple-integration", "version": "1.0.0"}
        })
        
        print("✅ MCP服务器已启动")
    
    async def send_mcp_request(self, method: str, params: dict = None):
        """发送MCP请求"""
        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        if params:
            request["params"] = params
        
        request_json = json.dumps(request) + "\n"
        self.mcp_process.stdin.write(request_json.encode())
        await self.mcp_process.stdin.drain()
        
        response_line = await self.mcp_process.stdout.readline()
        return json.loads(response_line.decode().strip())
    
    async def call_add_tool(self, a: float, b: float) -> str:
        """调用加法工具"""
        response = await self.send_mcp_request("tools/call", {
            "name": "add",
            "arguments": {"a": a, "b": b}
        })
        
        if "error" in response:
            return f"❌ 计算错误: {response['error']['message']}"
        
        content = response["result"].get("content", [])
        if content and content[0].get("type") == "text":
            return content[0]["text"]
        return str(response["result"])
    
    def extract_numbers_for_addition(self, text: str):
        """从文本中提取加法运算的数字"""
        import re

        # 简单的关键词检测
        if any(keyword in text.lower() for keyword in ['计算', '加', '+', '加上', '算']):
            # 尝试提取数字
            numbers = re.findall(r'-?\d+(?:\.\d+)?', text)
            if len(numbers) >= 2:
                try:
                    return float(numbers[0]), float(numbers[1])
                except ValueError:
                    pass

        return None, None
    
    async def process_message(self, user_message: str) -> str:
        """处理用户消息"""
        print(f"👤 用户: {user_message}")
        
        # 检查是否包含加法计算请求
        a, b = self.extract_numbers_for_addition(user_message)
        
        if a is not None and b is not None:
            print(f"🔧 检测到加法计算: {a} + {b}")
            
            # 直接调用MCP工具并返回结果
            calc_result = await self.call_add_tool(a, b)
            print(f"📊 MCP工具结果: {calc_result}")
            
            # 直接返回计算结果，不需要AI进一步处理
            return f"✅ {calc_result}"
        else:
            # 检查是否询问能力
            if any(keyword in user_message.lower() for keyword in ['能做什么', '功能', '帮助', '你好', 'hello']):
                return "👋 你好！我是一个AI助手，可以帮您进行加法计算。\n\n🔢 支持的功能:\n• 加法计算 (例如: 15 + 27)\n• 支持小数和负数\n• 实时计算结果\n\n💡 试试问我: '请计算 15 + 27'"
            
            # 其他对话使用OpenAI处理
            response = self.openai_client.chat.completions.create(
                model="deepseek-coder",
                messages=[
                    {"role": "system", "content": "你是一个友好的助手。如果用户需要加法计算，告诉他们可以直接说'计算 X + Y'。"},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.3
            )
            
            return response.choices[0].message.content
    
    async def close(self):
        """关闭MCP服务器"""
        if self.mcp_process:
            self.mcp_process.terminate()
            await self.mcp_process.wait()
            print("🔧 MCP服务器已关闭")

async def demo_simple_integration():
    """演示简单集成"""
    print("🎯 简单 OpenAI + MCP 集成演示")
    print("=" * 40)
    
    integration = SimpleOpenAIMCP()
    
    try:
        await integration.start_mcp_server()
        
        # 测试消息
        test_messages = [
            "计算 15 + 27",
            "请算一下 3.14 + 2.86",
            "100 + (-50) 等于多少？",
            "5.5加上4.5",
            "-10 + 25",
            "你好，你能做什么？",
            "今天天气怎么样？"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n{i}️⃣ 测试: {message}")
            print("-" * 30)
            
            response = await integration.process_message(message)
            print(f"🤖 回复: {response}")
        
        print("\n🎉 演示完成!")
        print("\n💡 简单集成的特点:")
        print("   ✅ 直接返回MCP工具结果")
        print("   ✅ 快速响应，无需AI重新处理")
        print("   ✅ 准确的计算结果")
        print("   ✅ 支持负数和小数")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
    finally:
        await integration.close()

async def interactive_simple_chat():
    """交互式简单聊天"""
    print("\n💬 交互式 OpenAI + MCP 聊天")
    print("🔢 支持加法计算，例如: '计算 15 + 27'")
    print("输入 'quit' 退出")
    print("-" * 40)
    
    integration = SimpleOpenAIMCP()
    
    try:
        await integration.start_mcp_server()
        
        while True:
            try:
                user_input = input("\n👤 您: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见!")
                    break
                
                if not user_input:
                    print("请输入有效的消息!")
                    continue
                
                print()
                response = await integration.process_message(user_input)
                print(f"🤖 AI: {response}")
                
            except KeyboardInterrupt:
                print("\n\n聊天被用户中断")
                break
            except Exception as e:
                print(f"\n❌ 错误: {e}")
    
    finally:
        await integration.close()

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "chat":
        # 交互式聊天模式
        asyncio.run(interactive_simple_chat())
    else:
        # 演示模式
        asyncio.run(demo_simple_integration())

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序失败: {e}")
        sys.exit(1)
