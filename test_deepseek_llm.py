#!/usr/bin/env python3
"""
测试deepseek-llm模型
"""

from openai import OpenAI

def test_deepseek_llm():
    """测试deepseek-llm模型"""
    client = OpenAI(
        base_url="http://localhost:11434/v1",
        api_key="ollama"
    )
    
    print("🧪 测试deepseek-llm模型")
    print("=" * 30)
    
    # 简单测试
    print("\n1️⃣ 简单对话测试:")
    try:
        response = client.chat.completions.create(
            model="deepseek-llm",
            messages=[
                {"role": "user", "content": "你好，请简单介绍一下你自己"}
            ],
            temperature=0.1
        )
        print(f"✅ 回复: {response.choices[0].message.content}")
    except Exception as e:
        print(f"❌ 失败: {e}")
    
    # 数学解析测试
    print("\n2️⃣ 数学解析测试:")
    test_inputs = [
        "计算 15 + 27",
        "请算一下 3.14 + 2.86", 
        "今天天气怎么样？"
    ]
    
    for i, test_input in enumerate(test_inputs, 1):
        print(f"\n测试 {i}: {test_input}")
        try:
            prompt = f"""分析用户输入是否包含加法计算。如果是，提取两个数字。

用户输入: {test_input}

如果是加法计算，回复格式: ADD:数字1,数字2
如果不是加法计算，回复: NOT_ADD

示例:
- "计算 15 + 27" → ADD:15,27
- "15加上27" → ADD:15,27  
- "今天天气怎么样" → NOT_ADD"""

            response = client.chat.completions.create(
                model="deepseek-llm",
                messages=[
                    {"role": "system", "content": "你是数学表达式解析器。严格按照要求的格式回复，不要添加解释。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            result = response.choices[0].message.content.strip()
            print(f"   结果: {result}")
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")

if __name__ == "__main__":
    test_deepseek_llm()
