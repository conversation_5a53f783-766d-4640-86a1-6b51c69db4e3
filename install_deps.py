#!/usr/bin/env python3
"""
安装项目依赖的脚本
"""

import subprocess
import sys

def install_dependencies():
    """安装项目依赖"""
    print("🔧 正在安装项目依赖...")
    
    try:
        # 使用pip安装requirements.txt中的依赖
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖安装成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def check_ollama():
    """检查Ollama是否运行"""
    print("\n🔍 检查Ollama服务...")
    
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama服务正在运行")
            return True
        else:
            print("❌ Ollama服务响应异常")
            return False
    except Exception as e:
        print(f"❌ 无法连接到Ollama服务: {e}")
        print("请确保:")
        print("  1. Ollama已安装")
        print("  2. 运行 'ollama serve' 启动服务")
        print("  3. 运行 'ollama pull deepseek-coder' 下载模型")
        return False

if __name__ == "__main__":
    print("🚀 DeepSeek-Coder Ollama 客户端安装程序")
    print("=" * 50)
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 检查Ollama
    check_ollama()
    
    print("\n🎉 安装完成!")
    print("现在可以运行: python3 main.py")
