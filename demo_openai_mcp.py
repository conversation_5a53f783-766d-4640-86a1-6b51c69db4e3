#!/usr/bin/env python3
"""
OpenAI + MCP 演示脚本
展示如何在OpenAI对话中使用MCP工具
"""

import asyncio
import json
import subprocess
import sys
from openai import OpenAI

class SimpleMCPClient:
    """简化的MCP客户端"""
    
    def __init__(self):
        self.process = None
        self.request_id = 0
    
    async def start_mcp_server(self):
        """启动MCP服务器"""
        self.process = await asyncio.create_subprocess_exec(
            sys.executable, "mcp_server.py",
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # 初始化
        await self.send_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {"name": "demo", "version": "1.0.0"}
        })
        
        print("✅ MCP服务器已启动并初始化")
    
    async def get_tools_for_openai(self):
        """获取OpenAI格式的工具定义"""
        response = await self.send_request("tools/list")
        mcp_tools = response["result"]["tools"]
        
        # 转换为OpenAI函数格式
        openai_tools = []
        for tool in mcp_tools:
            openai_tools.append({
                "type": "function",
                "function": {
                    "name": tool["name"],
                    "description": tool["description"],
                    "parameters": tool["inputSchema"]
                }
            })
        
        return openai_tools
    
    async def call_mcp_tool(self, tool_name: str, arguments: dict):
        """调用MCP工具"""
        response = await self.send_request("tools/call", {
            "name": tool_name,
            "arguments": arguments
        })
        
        if "error" in response:
            return f"错误: {response['error']['message']}"
        
        content = response["result"].get("content", [])
        if content and content[0].get("type") == "text":
            return content[0]["text"]
        return str(response["result"])
    
    async def send_request(self, method: str, params: dict = None):
        """发送MCP请求"""
        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        if params:
            request["params"] = params
        
        request_json = json.dumps(request) + "\n"
        self.process.stdin.write(request_json.encode())
        await self.process.stdin.drain()
        
        response_line = await self.process.stdout.readline()
        return json.loads(response_line.decode().strip())
    
    async def close(self):
        """关闭MCP服务器"""
        if self.process:
            self.process.terminate()
            await self.process.wait()

async def demo_openai_with_mcp():
    """演示OpenAI + MCP集成"""
    print("🎯 OpenAI + MCP 集成演示")
    print("=" * 40)
    
    # 初始化OpenAI客户端 (连接到本地Ollama)
    openai_client = OpenAI(
        base_url="http://localhost:11434/v1",
        api_key="ollama"
    )
    
    # 初始化MCP客户端
    mcp_client = SimpleMCPClient()
    
    try:
        # 启动MCP服务器
        await mcp_client.start_mcp_server()
        
        # 获取MCP工具
        tools = await mcp_client.get_tools_for_openai()
        print(f"📦 已加载 {len(tools)} 个MCP工具")
        
        # 测试对话
        test_messages = [
            "请帮我计算 15 + 27",
            "用加法工具算一下 3.14 + 2.86 等于多少",
            "计算 100 + (-50) 的结果",
            "你好，请介绍一下你自己"  # 不需要工具的对话
        ]
        
        for i, user_message in enumerate(test_messages, 1):
            print(f"\n{i}️⃣ 测试对话: {user_message}")
            print("-" * 30)
            
            # 构建对话消息
            messages = [
                {"role": "system", "content": "你是一个有用的助手。当用户需要计算时，使用可用的工具来帮助计算。"},
                {"role": "user", "content": user_message}
            ]
            
            # 调用OpenAI API
            response = openai_client.chat.completions.create(
                model="deepseek-coder",
                messages=messages,
                tools=tools,
                tool_choice="auto",
                temperature=0.1
            )
            
            message = response.choices[0].message
            
            # 检查是否有工具调用
            if message.tool_calls:
                print("🔧 AI决定使用工具:")
                
                for tool_call in message.tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    print(f"   调用: {function_name}({function_args})")
                    
                    # 执行MCP工具
                    tool_result = await mcp_client.call_mcp_tool(function_name, function_args)
                    print(f"   结果: {tool_result}")
                    
                    # 添加工具结果到对话
                    messages.append({
                        "role": "assistant",
                        "content": message.content,
                        "tool_calls": [{
                            "id": tool_call.id,
                            "type": "function",
                            "function": {
                                "name": function_name,
                                "arguments": tool_call.function.arguments
                            }
                        }]
                    })
                    
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": tool_result
                    })
                
                # 获取最终回复
                final_response = openai_client.chat.completions.create(
                    model="deepseek-coder",
                    messages=messages,
                    temperature=0.1
                )
                
                print(f"🤖 AI最终回复: {final_response.choices[0].message.content}")
            else:
                print(f"🤖 AI直接回复: {message.content}")
        
        print("\n🎉 演示完成!")
        print("\n💡 关键特性:")
        print("   ✅ OpenAI自动决定何时使用MCP工具")
        print("   ✅ 工具调用结果自动集成到对话中")
        print("   ✅ 支持多轮对话和工具链调用")
        print("   ✅ 错误处理和回退机制")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
    finally:
        await mcp_client.close()

if __name__ == "__main__":
    try:
        asyncio.run(demo_openai_with_mcp())
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"演示失败: {e}")
        sys.exit(1)
