#!/usr/bin/env python3
"""
简单的数据库查询Agent
使用本地Ollama deepseek-coder实现自然语言到SQL的转换
"""

import sqlite3
import json
from openai import OpenAI

class SimpleDatabaseAgent:
    """简单的数据库查询Agent"""
    
    def __init__(self, model_name: str = "deepseek-coder", db_path: str = "demo.db"):
        """
        初始化数据库查询Agent

        Args:
            model_name (str): Ollama模型名称
            db_path (str): SQLite数据库路径
        """
        self.model_name = model_name
        self.db_path = db_path

        # 初始化OpenAI客户端（连接到本地Ollama）
        self.client = OpenAI(
            base_url="http://localhost:11434/v1",
            api_key="ollama"
        )
        
        # 创建示例数据库
        self.create_demo_database()
        
        # 获取数据库结构
        self.schema = self.get_database_schema()
    
    def create_demo_database(self):
        """创建示例数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建用户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                age INTEGER,
                city TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建订单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                product_name TEXT NOT NULL,
                quantity INTEGER DEFAULT 1,
                price DECIMAL(10,2),
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # 清空现有数据
        cursor.execute('DELETE FROM orders')
        cursor.execute('DELETE FROM users')
        
        # 插入示例数据
        users_data = [
            ('张三', '<EMAIL>', 25, '北京'),
            ('李四', '<EMAIL>', 30, '上海'),
            ('王五', '<EMAIL>', 28, '广州'),
            ('赵六', '<EMAIL>', 35, '深圳'),
            ('钱七', '<EMAIL>', 22, '杭州')
        ]
        
        cursor.executemany(
            'INSERT INTO users (name, email, age, city) VALUES (?, ?, ?, ?)',
            users_data
        )
        
        orders_data = [
            (1, '笔记本电脑', 1, 5999.00),
            (1, '鼠标', 2, 99.00),
            (2, '手机', 1, 3999.00),
            (2, '耳机', 1, 299.00),
            (3, '键盘', 1, 399.00),
            (4, '显示器', 2, 1299.00),
            (5, '平板电脑', 1, 2999.00)
        ]
        
        cursor.executemany(
            'INSERT INTO orders (user_id, product_name, quantity, price) VALUES (?, ?, ?, ?)',
            orders_data
        )
        
        conn.commit()
        conn.close()
        print("✅ 示例数据库创建完成")
    
    def get_database_schema(self) -> str:
        """获取数据库结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        schema_info = "数据库结构:\n"
        for table in tables:
            table_name = table[0]
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            schema_info += f"\n表: {table_name}\n"
            for col in columns:
                schema_info += f"  - {col[1]} ({col[2]})\n"
        
        conn.close()
        return schema_info
    
    def natural_language_to_sql(self, question: str) -> str:
        """
        将自然语言转换为SQL查询
        
        Args:
            question (str): 自然语言问题
            
        Returns:
            str: SQL查询语句
        """
        try:
            prompt = f"""你是一个SQL专家。根据用户的自然语言问题，生成对应的SQL查询语句。

数据库结构:
{self.schema}

用户问题: {question}

重要规则:
1. 只返回一条SQL语句
2. 不要包含注释、解释或其他文本
3. 不要使用分号结尾
4. 不要包含多条语句

示例:
问题: "有多少个用户？"
SQL: SELECT COUNT(*) FROM users

问题: "显示所有用户的姓名和城市"
SQL: SELECT name, city FROM users

问题: "哪个城市的用户最多？"
SQL: SELECT city, COUNT(*) as count FROM users GROUP BY city ORDER BY count DESC LIMIT 1

问题: "总订单金额是多少？"
SQL: SELECT SUM(price * quantity) FROM orders

问题: "张三买了什么产品？"
SQL: SELECT o.product_name, o.quantity, o.price FROM orders o JOIN users u ON o.user_id = u.id WHERE u.name = '张三'

问题: "平均订单金额是多少？"
SQL: SELECT AVG(price * quantity) FROM orders

现在请为以下问题生成SQL:
{question}

SQL:"""

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "你是一个SQL专家，只返回SQL语句，不要其他内容。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            sql = response.choices[0].message.content.strip()

            # 清理SQL语句
            if sql.startswith("SQL:"):
                sql = sql[4:].strip()
            if sql.startswith("```sql"):
                sql = sql[6:].strip()
            if sql.endswith("```"):
                sql = sql[:-3].strip()

            # 移除注释和多余内容
            # 首先移除行内注释
            if '#' in sql:
                sql = sql.split('#')[0].strip()
            if '--' in sql:
                sql = sql.split('--')[0].strip()

            # 处理多行情况
            lines = sql.split('\n')
            clean_lines = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('--'):
                    # 移除行内注释
                    if '#' in line:
                        line = line.split('#')[0].strip()
                    if '--' in line:
                        line = line.split('--')[0].strip()
                    if line:
                        clean_lines.append(line)

            sql = ' '.join(clean_lines)

            # 移除分号
            if sql.endswith(';'):
                sql = sql[:-1].strip()

            # 移除括号内的注释
            import re
            sql = re.sub(r'\([^)]*注意[^)]*\)', '', sql)
            sql = re.sub(r'\([^)]*回答[^)]*\)', '', sql)

            return sql
            
        except Exception as e:
            print(f"⚠️ SQL生成失败: {e}")
            return self.fallback_sql_mapping(question)
    
    def fallback_sql_mapping(self, question: str) -> str:
        """回退的SQL映射"""
        question_lower = question.lower()
        
        if "多少个用户" in question or "用户数量" in question:
            return "SELECT COUNT(*) FROM users"
        elif "所有用户" in question and ("姓名" in question or "城市" in question):
            return "SELECT name, city FROM users"
        elif "哪个城市" in question and "最多" in question:
            return "SELECT city, COUNT(*) as count FROM users GROUP BY city ORDER BY count DESC LIMIT 1"
        elif "总订单金额" in question:
            return "SELECT SUM(price * quantity) FROM orders"
        elif "张三" in question and "买了" in question:
            return "SELECT o.product_name, o.quantity, o.price FROM orders o JOIN users u ON o.user_id = u.id WHERE u.name = '张三'"
        elif "平均订单金额" in question:
            return "SELECT AVG(price * quantity) FROM orders"
        else:
            return "SELECT ' 无法理解的问题' as message"
    
    def execute_sql(self, sql: str) -> str:
        """执行SQL查询"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(sql)
            results = cursor.fetchall()
            
            # 获取列名
            column_names = [description[0] for description in cursor.description]
            
            conn.close()
            
            # 格式化结果
            if not results:
                return "查询结果为空"
            
            # 转换为字典格式
            formatted_results = []
            for row in results:
                row_dict = dict(zip(column_names, row))
                formatted_results.append(row_dict)
            
            return json.dumps(formatted_results, ensure_ascii=False, indent=2)
            
        except Exception as e:
            return f"SQL执行失败: {str(e)}"
    
    def query(self, question: str) -> str:
        """
        处理自然语言查询
        
        Args:
            question (str): 自然语言问题
            
        Returns:
            str: 查询结果
        """
        print(f"🔍 问题: {question}")
        
        # 生成SQL
        sql = self.natural_language_to_sql(question)
        print(f"🔧 生成的SQL: {sql}")
        
        # 执行SQL
        result = self.execute_sql(sql)
        print(f"📊 查询结果: {result}")
        
        return result

def main():
    """主函数"""
    print("🚀 简单数据库查询Agent")
    print("=" * 40)
    
    try:
        # 初始化Agent
        agent = SimpleDatabaseAgent()
        
        # 显示数据库结构
        print("\n📊 数据库结构:")
        print("-" * 30)
        print(agent.schema)
        
        # 示例查询
        print("\n🧪 示例查询:")
        print("-" * 30)
        
        test_questions = [
            "有多少个用户？",
            "显示所有用户的姓名和城市",
            "哪个城市的用户最多？",
            "总订单金额是多少？",
            "张三买了什么产品？",
            "平均订单金额是多少？"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{i}️⃣ 测试查询")
            print("-" * 20)
            agent.query(question)
        
        # 交互式查询
        print("\n💬 交互式查询模式")
        print("输入自然语言问题，输入'quit'退出")
        print("-" * 40)
        
        while True:
            try:
                user_question = input("\n🤔 您的问题: ").strip()
                
                if user_question.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见!")
                    break
                
                if not user_question:
                    print("请输入有效的问题!")
                    continue
                
                print()
                agent.query(user_question)
                
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"\n❌ 错误: {e}")
    
    except Exception as e:
        print(f"❌ 初始化失败: {e}")

if __name__ == "__main__":
    main()
