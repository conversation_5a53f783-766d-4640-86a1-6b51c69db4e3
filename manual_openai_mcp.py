#!/usr/bin/env python3
"""
手动OpenAI + MCP集成
当模型不支持工具调用时，手动解析和执行MCP工具
"""

import asyncio
import json
import re
import subprocess
import sys
from openai import OpenAI

class ManualMCPIntegration:
    """手动MCP集成"""
    
    def __init__(self):
        self.openai_client = OpenAI(
            base_url="http://localhost:11434/v1",
            api_key="ollama"
        )
        self.mcp_process = None
        self.request_id = 0
    
    async def start_mcp_server(self):
        """启动MCP服务器"""
        self.mcp_process = await asyncio.create_subprocess_exec(
            sys.executable, "mcp_server.py",
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # 初始化MCP
        await self.send_mcp_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {"name": "manual-integration", "version": "1.0.0"}
        })
        
        print("✅ MCP服务器已启动")
    
    async def send_mcp_request(self, method: str, params: dict = None):
        """发送MCP请求"""
        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        if params:
            request["params"] = params
        
        request_json = json.dumps(request) + "\n"
        self.mcp_process.stdin.write(request_json.encode())
        await self.mcp_process.stdin.drain()
        
        response_line = await self.mcp_process.stdout.readline()
        return json.loads(response_line.decode().strip())
    
    async def call_add_tool(self, a: float, b: float) -> str:
        """调用加法工具"""
        response = await self.send_mcp_request("tools/call", {
            "name": "add",
            "arguments": {"a": a, "b": b}
        })
        
        if "error" in response:
            return f"计算错误: {response['error']['message']}"
        
        content = response["result"].get("content", [])
        if content and content[0].get("type") == "text":
            return content[0]["text"]
        return str(response["result"])
    
    def extract_calculation_request(self, text: str):
        """从文本中提取计算请求"""
        # 匹配各种加法表达式
        patterns = [
            r'(\d+(?:\.\d+)?)\s*\+\s*(\d+(?:\.\d+)?)',  # 数字 + 数字
            r'计算\s*(\d+(?:\.\d+)?)\s*\+\s*(\d+(?:\.\d+)?)',  # 计算 数字 + 数字
            r'(\d+(?:\.\d+)?)\s*加\s*(\d+(?:\.\d+)?)',  # 数字 加 数字
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    a = float(match.group(1))
                    b = float(match.group(2))
                    return a, b
                except ValueError:
                    continue
        
        return None, None
    
    async def process_message(self, user_message: str) -> str:
        """处理用户消息"""
        print(f"👤 用户: {user_message}")
        
        # 检查是否包含计算请求
        a, b = self.extract_calculation_request(user_message)
        
        if a is not None and b is not None:
            print(f"🔧 检测到计算请求: {a} + {b}")
            
            # 调用MCP工具
            calc_result = await self.call_add_tool(a, b)
            print(f"📊 MCP工具结果: {calc_result}")
            
            # 构建包含工具结果的提示
            enhanced_prompt = f"""用户问: {user_message}

计算工具返回: {calc_result}

请简洁地告诉用户计算结果。"""

            # 调用OpenAI生成回复
            response = self.openai_client.chat.completions.create(
                model="deepseek-coder",
                messages=[
                    {"role": "system", "content": "你是一个数学助手。用户请求计算，我已经用工具计算出结果。请简洁友好地回复用户，直接告诉他们答案。"},
                    {"role": "user", "content": enhanced_prompt}
                ],
                temperature=0.1
            )
            
            return response.choices[0].message.content
        else:
            # 普通对话，不需要工具
            response = self.openai_client.chat.completions.create(
                model="deepseek-coder",
                messages=[
                    {"role": "system", "content": "你是一个有用的助手。如果用户需要数学计算，请告诉他们你可以帮助计算加法。"},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.1
            )
            
            return response.choices[0].message.content
    
    async def close(self):
        """关闭MCP服务器"""
        if self.mcp_process:
            self.mcp_process.terminate()
            await self.mcp_process.wait()
            print("🔧 MCP服务器已关闭")

async def demo_manual_integration():
    """演示手动集成"""
    print("🎯 手动 OpenAI + MCP 集成演示")
    print("=" * 40)
    
    integration = ManualMCPIntegration()
    
    try:
        await integration.start_mcp_server()
        
        # 测试消息
        test_messages = [
            "请帮我计算 15 + 27",
            "算一下 3.14 + 2.86",
            "100 + (-50) 等于多少？",
            "5.5加上4.5是多少",
            "你好，你能做什么？",  # 不需要计算的对话
            "今天天气怎么样？"  # 普通对话
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n{i}️⃣ 测试 {i}")
            print("-" * 30)
            
            response = await integration.process_message(message)
            print(f"🤖 AI回复: {response}")
        
        print("\n🎉 演示完成!")
        print("\n💡 手动集成的优势:")
        print("   ✅ 兼容不支持工具调用的模型")
        print("   ✅ 可以自定义工具调用逻辑")
        print("   ✅ 灵活的文本解析和处理")
        print("   ✅ 完全控制工具使用时机")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
    finally:
        await integration.close()

async def interactive_manual_chat():
    """交互式手动聊天"""
    print("\n💬 交互式 OpenAI + MCP 聊天")
    print("输入包含加法的消息来测试MCP工具调用")
    print("输入 'quit' 退出")
    print("-" * 40)
    
    integration = ManualMCPIntegration()
    
    try:
        await integration.start_mcp_server()
        
        while True:
            try:
                user_input = input("\n👤 您: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("再见!")
                    break
                
                if not user_input:
                    print("请输入有效的消息!")
                    continue
                
                print()
                response = await integration.process_message(user_input)
                print(f"🤖 AI: {response}")
                
            except KeyboardInterrupt:
                print("\n\n聊天被用户中断")
                break
            except Exception as e:
                print(f"\n❌ 错误: {e}")
    
    finally:
        await integration.close()

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "chat":
        # 交互式聊天模式
        asyncio.run(interactive_manual_chat())
    else:
        # 演示模式
        asyncio.run(demo_manual_integration())

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序失败: {e}")
        sys.exit(1)
