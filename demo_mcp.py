#!/usr/bin/env python3
"""
MCP Server 演示脚本
展示如何与MCP Addition Server交互
"""

import json
import subprocess
import sys

def send_mcp_request(request):
    """发送MCP请求并获取响应"""
    # 启动MCP服务器进程
    process = subprocess.Popen(
        [sys.executable, "mcp_server.py"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # 发送请求
    request_json = json.dumps(request)
    stdout, stderr = process.communicate(input=request_json + "\n")
    
    # 解析响应
    if stdout.strip():
        return json.loads(stdout.strip())
    else:
        return {"error": f"No response. stderr: {stderr}"}

def demo_mcp_server():
    """演示MCP服务器功能"""
    print("🎯 MCP Addition Server 演示")
    print("=" * 40)
    
    # 1. 初始化
    print("\n1️⃣ 初始化连接...")
    init_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "demo-client",
                "version": "1.0.0"
            }
        }
    }
    
    response = send_mcp_request(init_request)
    if "result" in response:
        server_info = response["result"].get("serverInfo", {})
        print(f"✅ 连接成功! 服务器: {server_info.get('name')} v{server_info.get('version')}")
    else:
        print(f"❌ 初始化失败: {response}")
        return
    
    # 2. 获取工具列表
    print("\n2️⃣ 获取可用工具...")
    tools_request = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/list"
    }
    
    response = send_mcp_request(tools_request)
    if "result" in response:
        tools = response["result"].get("tools", [])
        print(f"✅ 找到 {len(tools)} 个工具:")
        for tool in tools:
            print(f"   📦 {tool['name']}: {tool['description']}")
    else:
        print(f"❌ 获取工具失败: {response}")
        return
    
    # 3. 测试加法计算
    print("\n3️⃣ 测试加法计算...")
    test_cases = [
        (15, 27),
        (3.14, 2.86),
        (-10, 25),
        (0, 100)
    ]
    
    for i, (a, b) in enumerate(test_cases, 1):
        print(f"\n   计算 {i}: {a} + {b}")
        
        calc_request = {
            "jsonrpc": "2.0",
            "id": 2 + i,
            "method": "tools/call",
            "params": {
                "name": "add",
                "arguments": {
                    "a": a,
                    "b": b
                }
            }
        }
        
        response = send_mcp_request(calc_request)
        if "result" in response:
            content = response["result"].get("content", [])
            if content and content[0].get("type") == "text":
                print(f"   ✅ {content[0]['text']}")
            else:
                print(f"   ✅ 结果: {response['result']}")
        else:
            print(f"   ❌ 计算失败: {response}")
    
    # 4. 测试错误处理
    print("\n4️⃣ 测试错误处理...")
    error_request = {
        "jsonrpc": "2.0",
        "id": 10,
        "method": "tools/call",
        "params": {
            "name": "add",
            "arguments": {
                "a": "not_a_number",
                "b": 5
            }
        }
    }
    
    response = send_mcp_request(error_request)
    if "error" in response:
        print(f"   ✅ 正确处理错误: {response['error']['message']}")
    else:
        print(f"   ❌ 应该返回错误，但得到: {response}")
    
    print("\n🎉 演示完成!")
    print("\n💡 提示:")
    print("   - 运行 'python3 mcp_client.py' 进行完整测试")
    print("   - 查看 'MCP_README.md' 了解详细使用方法")
    print("   - 可以将此服务器集成到Claude Desktop中使用")

if __name__ == "__main__":
    try:
        demo_mcp_server()
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"演示失败: {e}")
        sys.exit(1)
