# MCP Addition Server

这是一个简单的 MCP (Model Context Protocol) 服务器实现，提供 a + b 的计算功能。

## 文件说明

- `mcp_server.py` - MCP服务器主程序
- `mcp_client.py` - 测试客户端
- `mcp_config.json` - MCP配置文件
- `MCP_README.md` - 本说明文档

## 功能特性

### 支持的工具

1. **add** - 计算两个数字的和
   - 参数: `a` (number), `b` (number)
   - 返回: 计算结果文本

### MCP协议支持

- ✅ 初始化 (`initialize`)
- ✅ 工具列表 (`tools/list`)
- ✅ 工具调用 (`tools/call`)
- ✅ 错误处理
- ✅ JSON-RPC 2.0 协议

## 使用方法

### 1. 直接测试

运行测试客户端来验证服务器功能：

```bash
python3 mcp_client.py
```

### 2. 手动测试

启动服务器：
```bash
python3 mcp_server.py
```

然后发送JSON-RPC请求：

#### 初始化
```json
{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0.0"}}}
```

#### 获取工具列表
```json
{"jsonrpc": "2.0", "id": 2, "method": "tools/list"}
```

#### 调用加法工具
```json
{"jsonrpc": "2.0", "id": 3, "method": "tools/call", "params": {"name": "add", "arguments": {"a": 5, "b": 3}}}
```

### 3. 与Claude Desktop集成

将以下配置添加到Claude Desktop的配置文件中：

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "addition-server": {
      "command": "python3",
      "args": ["/path/to/your/mcp_server.py"],
      "cwd": "/path/to/your/directory"
    }
  }
}
```

## 示例输出

### 成功调用
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "计算结果: 5 + 3 = 8"
      }
    ]
  }
}
```

### 错误响应
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "error": {
    "code": -32602,
    "message": "Parameters 'a' and 'b' must be numbers"
  }
}
```

## 扩展功能

您可以轻松扩展这个服务器来支持更多数学运算：

1. 在 `MCPServer.__init__()` 中添加新工具定义
2. 在 `handle_tools_call()` 中添加新的处理逻辑
3. 实现对应的处理函数

例如，添加减法功能：

```python
"subtract": {
    "name": "subtract",
    "description": "计算两个数字的差 (a - b)",
    "inputSchema": {
        "type": "object",
        "properties": {
            "a": {"type": "number", "description": "被减数"},
            "b": {"type": "number", "description": "减数"}
        },
        "required": ["a", "b"]
    }
}
```

## 故障排除

1. **服务器无法启动**
   - 检查Python版本 (需要3.7+)
   - 确保有执行权限

2. **JSON解析错误**
   - 确保请求格式正确
   - 检查JSON语法

3. **工具调用失败**
   - 验证参数类型和必需参数
   - 查看错误消息获取详细信息

## 技术细节

- **协议版本**: MCP 2024-11-05
- **传输方式**: stdio (标准输入输出)
- **消息格式**: JSON-RPC 2.0
- **Python版本**: 3.7+
- **依赖**: 仅使用Python标准库
