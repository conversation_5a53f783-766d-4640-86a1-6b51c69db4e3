#!/usr/bin/env python3
"""
LangChain + Ollama 数据库查询Agent
使用本地Ollama模型和LangChain实现智能数据库查询功能
"""

import sqlite3
from typing import List, Dict, Any
from langchain_community.llms import Ollama
from langchain.agents import create_sql_agent
from langchain.agents.agent_toolkits import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents.agent_types import AgentType

class LocalDatabaseQueryAgent:
    """本地数据库查询Agent"""
    
    def __init__(self, model_name: str = "deepseek-coder", db_path: str = "demo.db"):
        """
        初始化数据库查询Agent
        
        Args:
            model_name (str): Ollama模型名称
            db_path (str): SQLite数据库路径
        """
        self.model_name = model_name
        self.db_path = db_path
        
        # 初始化Ollama模型
        self.llm = Ollama(
            model=model_name,
            base_url="http://localhost:11434",
            temperature=0.1
        )
        
        # 创建示例数据库
        self.create_demo_database()
        
        # 初始化数据库连接
        self.db = SQLDatabase.from_uri(f"sqlite:///{db_path}")
        
        # 创建SQL工具包
        self.toolkit = SQLDatabaseToolkit(db=self.db, llm=self.llm)
        
        # 创建SQL Agent
        self.agent = create_sql_agent(
            llm=self.llm,
            toolkit=self.toolkit,
            verbose=True,
            agent_type=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
            handle_parsing_errors=True
        )
    
    def create_demo_database(self):
        """创建示例数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建用户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                age INTEGER,
                city TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建订单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                product_name TEXT NOT NULL,
                quantity INTEGER DEFAULT 1,
                price DECIMAL(10,2),
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # 清空现有数据
        cursor.execute('DELETE FROM orders')
        cursor.execute('DELETE FROM users')
        
        # 插入示例数据
        users_data = [
            ('张三', '<EMAIL>', 25, '北京'),
            ('李四', '<EMAIL>', 30, '上海'),
            ('王五', '<EMAIL>', 28, '广州'),
            ('赵六', '<EMAIL>', 35, '深圳'),
            ('钱七', '<EMAIL>', 22, '杭州')
        ]
        
        cursor.executemany(
            'INSERT INTO users (name, email, age, city) VALUES (?, ?, ?, ?)',
            users_data
        )
        
        orders_data = [
            (1, '笔记本电脑', 1, 5999.00),
            (1, '鼠标', 2, 99.00),
            (2, '手机', 1, 3999.00),
            (2, '耳机', 1, 299.00),
            (3, '键盘', 1, 399.00),
            (4, '显示器', 2, 1299.00),
            (5, '平板电脑', 1, 2999.00)
        ]
        
        cursor.executemany(
            'INSERT INTO orders (user_id, product_name, quantity, price) VALUES (?, ?, ?, ?)',
            orders_data
        )
        
        conn.commit()
        conn.close()
        print("✅ 示例数据库创建完成")
    
    def simple_query(self, question: str) -> str:
        """
        简化的数据库查询（不使用Agent）
        
        Args:
            question (str): 查询问题
            
        Returns:
            str: 查询结果
        """
        try:
            # 简单的问题映射到SQL
            question_lower = question.lower()
            
            if "多少个用户" in question or "用户数量" in question:
                sql = "SELECT COUNT(*) as user_count FROM users"
            elif "所有用户" in question and ("姓名" in question or "城市" in question):
                sql = "SELECT name, city FROM users"
            elif "哪个城市" in question and "最多" in question:
                sql = "SELECT city, COUNT(*) as count FROM users GROUP BY city ORDER BY count DESC LIMIT 1"
            elif "总订单金额" in question:
                sql = "SELECT SUM(price * quantity) as total_amount FROM orders"
            elif "张三" in question and "买了" in question:
                sql = "SELECT o.product_name, o.quantity, o.price FROM orders o JOIN users u ON o.user_id = u.id WHERE u.name = '张三'"
            elif "平均订单金额" in question:
                sql = "SELECT AVG(price * quantity) as avg_amount FROM orders"
            else:
                return "抱歉，我还不能理解这个问题。请尝试问一些关于用户数量、订单金额等的问题。"
            
            print(f"🔍 执行SQL: {sql}")
            result = self.db.run(sql)
            return str(result)
            
        except Exception as e:
            return f"查询失败: {str(e)}"
    
    def query_database(self, question: str) -> str:
        """
        使用Agent查询数据库
        
        Args:
            question (str): 自然语言查询问题
            
        Returns:
            str: 查询结果
        """
        try:
            print(f"🔍 查询问题: {question}")
            result = self.agent.run(question)
            return result
        except Exception as e:
            print(f"⚠️ Agent查询失败，使用简化查询: {e}")
            return self.simple_query(question)
    
    def get_database_schema(self) -> str:
        """获取数据库结构信息"""
        try:
            schema_info = self.db.get_table_info()
            return schema_info
        except Exception as e:
            return f"获取数据库结构失败: {str(e)}"
    
    def execute_sql(self, sql: str) -> str:
        """
        直接执行SQL查询
        
        Args:
            sql (str): SQL查询语句
            
        Returns:
            str: 查询结果
        """
        try:
            result = self.db.run(sql)
            return str(result)
        except Exception as e:
            return f"SQL执行失败: {str(e)}"

def main():
    """主函数"""
    print("🚀 LangChain + Ollama 数据库查询Agent")
    print("=" * 50)
    
    try:
        # 初始化Agent
        print("🔧 初始化Agent...")
        agent = LocalDatabaseQueryAgent()
        
        # 显示数据库结构
        print("\n📊 数据库结构:")
        print("-" * 30)
        schema = agent.get_database_schema()
        print(schema)
        
        # 示例查询
        print("\n🧪 示例查询:")
        print("-" * 30)
        
        test_questions = [
            "有多少个用户？",
            "显示所有用户的姓名和城市",
            "哪个城市的用户最多？",
            "总订单金额是多少？",
            "张三买了什么产品？",
            "平均订单金额是多少？"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{i}️⃣ {question}")
            print("-" * 20)
            try:
                answer = agent.simple_query(question)  # 使用简化查询
                print(f"📋 回答: {answer}")
            except Exception as e:
                print(f"❌ 查询失败: {e}")
        
        # 交互式查询
        print("\n💬 交互式查询模式")
        print("输入自然语言问题，输入'quit'退出")
        print("支持的问题类型:")
        print("- 用户数量相关")
        print("- 订单金额相关")
        print("- 城市统计相关")
        print("-" * 40)
        
        while True:
            try:
                user_question = input("\n🤔 您的问题: ").strip()
                
                if user_question.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见!")
                    break
                
                if not user_question:
                    print("请输入有效的问题!")
                    continue
                
                print()
                answer = agent.simple_query(user_question)
                print(f"📋 回答: {answer}")
                
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"\n❌ 错误: {e}")
    
    except Exception as e:
        print(f"❌ 初始化失败: {e}")

if __name__ == "__main__":
    main()
